{"openapi": "3.1.0", "info": {"title": "ComfyUI Workflow Service & Management API", "version": "0.1.0"}, "paths": {"/api/workflow": {"get": {"summary": "Get All Workflows Endpoint", "operationId": "get_all_workflows_endpoint_api_workflow_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Response Get All Workflows Endpoint Api Workflow Get"}}}}}}, "post": {"summary": "Publish Workflow Endpoint", "operationId": "publish_workflow_endpoint_api_workflow_post", "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/workflow/{workflow_name}": {"delete": {"summary": "Delete Workflow Endpoint", "operationId": "delete_workflow_endpoint_api_workflow__workflow_name__delete", "parameters": [{"name": "workflow_name", "in": "path", "required": true, "schema": {"type": "string", "description": "The full, unique name of the workflow to delete, e.g., 'my_workflow [20250101120000]'", "title": "Workflow Name"}, "description": "The full, unique name of the workflow to delete, e.g., 'my_workflow [20250101120000]'"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/run/": {"post": {"summary": "Execute Workflow Endpoint", "operationId": "execute_workflow_endpoint_api_run__post", "parameters": [{"name": "base_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Base Name"}}, {"name": "version", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Request Data Raw"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/spec/": {"get": {"summary": "Get Workflow Spec Endpoint", "operationId": "get_workflow_spec_endpoint_api_spec__get", "parameters": [{"name": "base_name", "in": "query", "required": true, "schema": {"type": "string", "title": "Base Name"}}, {"name": "version", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Version"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/servers/status": {"get": {"tags": ["Server Monitoring"], "summary": "Get Servers Status Endpoint", "description": "获取所有已配置的ComfyUI服务器的配置信息和实时状态。", "operationId": "get_servers_status_endpoint_api_servers_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ServerStatus"}, "type": "array", "title": "Response Get Servers Status Endpoint Api Servers Status Get"}}}}}}}, "/api/servers/{server_index}/files": {"get": {"tags": ["Server Monitoring"], "summary": "List Server Files Endpoint", "description": "获取指定ComfyUI服务器的输入和输出文件夹中的文件列表。", "operationId": "list_server_files_endpoint_api_servers__server_index__files_get", "parameters": [{"name": "server_index", "in": "path", "required": true, "schema": {"type": "integer", "minimum": 0, "description": "服务器在配置列表中的索引", "title": "Server Index"}, "description": "服务器在配置列表中的索引"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServerFiles"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "<PERSON> Root", "description": "提供一个API的快速使用指南。", "operationId": "read_root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"FileDetails": {"properties": {"name": {"type": "string", "title": "Name"}, "size_kb": {"type": "number", "title": "Size Kb"}, "modified_at": {"type": "string", "format": "date-time", "title": "Modified At"}}, "type": "object", "required": ["name", "size_kb", "modified_at"], "title": "FileDetails"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ServerFiles": {"properties": {"server_index": {"type": "integer", "title": "Server Index"}, "http_url": {"type": "string", "title": "Http Url"}, "input_files": {"items": {"$ref": "#/components/schemas/FileDetails"}, "type": "array", "title": "Input Files"}, "output_files": {"items": {"$ref": "#/components/schemas/FileDetails"}, "type": "array", "title": "Output Files"}}, "type": "object", "required": ["server_index", "http_url", "input_files", "output_files"], "title": "ServerFiles"}, "ServerQueueDetails": {"properties": {"running_count": {"type": "integer", "title": "Running Count"}, "pending_count": {"type": "integer", "title": "Pending Count"}}, "type": "object", "required": ["running_count", "pending_count"], "title": "ServerQueueDetails"}, "ServerStatus": {"properties": {"server_index": {"type": "integer", "title": "Server Index"}, "http_url": {"type": "string", "title": "Http Url"}, "ws_url": {"type": "string", "title": "Ws Url"}, "input_dir": {"type": "string", "title": "Input Dir"}, "output_dir": {"type": "string", "title": "Output Dir"}, "is_reachable": {"type": "boolean", "title": "Is Reachable"}, "is_free": {"type": "boolean", "title": "Is Free"}, "queue_details": {"$ref": "#/components/schemas/ServerQueueDetails"}}, "type": "object", "required": ["server_index", "http_url", "ws_url", "input_dir", "output_dir", "is_reachable", "is_free", "queue_details"], "title": "ServerStatus"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}