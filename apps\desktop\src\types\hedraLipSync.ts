/**
 * Hedra 口型合成工具类型定义
 * 基于 bowongTextVideoAgent API 的 Hedra 接口
 */

export interface HedraFileInfo {
  file: File;
  filePath?: string; // 本地文件路径
  url?: string; // 上传后的URL
  uploadStatus: 'idle' | 'uploading' | 'uploaded' | 'completed' | 'error';
  uploadProgress?: number;
  errorMessage?: string;
  uploadedUrl?: string; // 上传后的URL
}

export interface HedraTaskInfo {
  taskId?: string;
  status: 'idle' | 'submitting' | 'submitted' | 'processing' | 'completed' | 'failed';
  progress?: number;
  resultUrl?: string;
  errorMessage?: string;
  createdAt?: string;
  updatedAt?: string;
  message?: string;
}

export interface HedraLipSyncState {
  imageFile: HedraFileInfo | null;
  audioFile: HedraFileInfo | null;
  task: HedraTaskInfo;
  isProcessing: boolean;
}

export interface HedraUploadProgress {
  fileType: 'image' | 'audio';
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  errorMessage?: string;
}

export interface HedraTaskProgress {
  taskId: string;
  status: string;
  progress?: number;
  result?: any;
  error?: string;
  created_at?: string;
  updated_at?: string;
}

// 从 bowongTextVideoAgent 类型重新导出
export type {
  HedraFileUploadRequest,
  HedraTaskSubmitRequest,
  HedraTaskStatusParams,
  FileUploadResponse,
  TaskResponse,
  TaskStatusResponse
} from './bowongTextVideoAgent';
