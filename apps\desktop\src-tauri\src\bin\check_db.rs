use rusqlite::{Connection, Result};


fn main() -> Result<()> {
    // 获取数据库路径
    let app_data_dir = dirs::data_dir()
        .expect("无法获取应用数据目录")
        .join("mixvideo");
    
    let db_path = app_data_dir.join("mixvideoV2.db");
    
    println!("数据库路径: {}", db_path.display());
    
    if !db_path.exists() {
        println!("数据库文件不存在");
        return Ok(());
    }
    
    // 连接数据库
    let conn = Connection::open(&db_path)?;
    
    // 检查 outfit_image_records 表结构
    println!("\n=== outfit_image_records 表结构 ===");
    let mut stmt = conn.prepare("PRAGMA table_info(outfit_image_records)")?;
    let rows = stmt.query_map([], |row| {
        Ok((
            row.get::<_, i32>(0)?,      // cid
            row.get::<_, String>(1)?,   // name
            row.get::<_, String>(2)?,   // type
            row.get::<_, i32>(3)?,      // notnull
            row.get::<_, Option<String>>(4)?, // dflt_value
            row.get::<_, i32>(5)?,      // pk
        ))
    })?;
    
    for row in rows {
        let (cid, name, type_, notnull, dflt_value, pk) = row?;
        println!("{}: {} {} {} {} {}", cid, name, type_, notnull, dflt_value.unwrap_or("NULL".to_string()), pk);
    }
    
    // 检查迁移历史
    println!("\n=== 迁移历史 ===");
    let mut stmt = conn.prepare("SELECT version, description, applied_at, success FROM schema_migrations ORDER BY version")?;
    let rows = stmt.query_map([], |row| {
        Ok((
            row.get::<_, u32>(0)?,      // version
            row.get::<_, String>(1)?,   // description
            row.get::<_, String>(2)?,   // applied_at
            row.get::<_, i32>(3)?,      // success
        ))
    })?;
    
    for row in rows {
        let (version, description, applied_at, success) = row?;
        println!("v{}: {} - {} (成功: {})", version, description, applied_at, success == 1);
    }
    
    // 检查是否有 comfyui_prompt_id 字段
    println!("\n=== 检查 comfyui_prompt_id 字段 ===");
    match conn.prepare("SELECT comfyui_prompt_id FROM outfit_image_records LIMIT 1") {
        Ok(_) => println!("✅ comfyui_prompt_id 字段存在"),
        Err(e) => println!("❌ comfyui_prompt_id 字段不存在: {}", e),
    }
    
    Ok(())
}
