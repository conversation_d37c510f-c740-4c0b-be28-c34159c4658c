/**
 * 性能优化配置
 * 定义缓存、并发控制、重试等性能相关配置
 */

import { RetryConfig } from '../utils/bowongTextVideoAgentUtils';

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 最大缓存项数量 */
  maxSize: number;
  /** 默认TTL (毫秒) */
  defaultTTL: number;
  /** 是否启用缓存 */
  enabled: boolean;
  /** 自动清理间隔 (毫秒) */
  cleanupInterval: number;
}

/**
 * 并发控制配置
 */
export interface ConcurrencyConfig {
  /** 最大并发数 */
  maxConcurrency: number;
  /** 队列最大长度 */
  maxQueueSize: number;
  /** 是否启用并发控制 */
  enabled: boolean;
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
  /** 是否启用性能监控 */
  enabled: boolean;
  /** 慢操作阈值 (毫秒) */
  slowOperationThreshold: number;
  /** 是否记录详细时间 */
  detailedTiming: boolean;
  /** 性能数据保留时间 (毫秒) */
  dataRetentionTime: number;
}

/**
 * 网络优化配置
 */
export interface NetworkConfig {
  /** 连接超时 (毫秒) */
  connectionTimeout: number;
  /** 读取超时 (毫秒) */
  readTimeout: number;
  /** 重试配置 */
  retry: RetryConfig;
  /** 是否启用请求去重 */
  enableDeduplication: boolean;
  /** 请求去重窗口时间 (毫秒) */
  deduplicationWindow: number;
}

/**
 * 完整的性能配置
 */
export interface PerformanceConfig {
  cache: CacheConfig;
  concurrency: ConcurrencyConfig;
  monitoring: PerformanceMonitorConfig;
  network: NetworkConfig;
}

/**
 * 开发环境性能配置
 */
export const DEVELOPMENT_PERFORMANCE_CONFIG: PerformanceConfig = {
  cache: {
    maxSize: 500,
    defaultTTL: 2 * 60 * 1000, // 2分钟
    enabled: true,
    cleanupInterval: 30 * 1000, // 30秒
  },
  concurrency: {
    maxConcurrency: 3,
    maxQueueSize: 50,
    enabled: true,
  },
  monitoring: {
    enabled: true,
    slowOperationThreshold: 1000, // 1秒
    detailedTiming: true,
    dataRetentionTime: 10 * 60 * 1000, // 10分钟
  },
  network: {
    connectionTimeout: 10000, // 10秒
    readTimeout: 30000, // 30秒
    retry: {
      maxAttempts: 2,
      baseDelay: 500,
      maxDelay: 5000,
      backoffFactor: 1.5,
    },
    enableDeduplication: true,
    deduplicationWindow: 1000, // 1秒
  },
};

/**
 * 生产环境性能配置
 */
export const PRODUCTION_PERFORMANCE_CONFIG: PerformanceConfig = {
  cache: {
    maxSize: 2000,
    defaultTTL: 5 * 60 * 1000, // 5分钟
    enabled: true,
    cleanupInterval: 60 * 1000, // 1分钟
  },
  concurrency: {
    maxConcurrency: 8,
    maxQueueSize: 200,
    enabled: true,
  },
  monitoring: {
    enabled: true,
    slowOperationThreshold: 2000, // 2秒
    detailedTiming: false,
    dataRetentionTime: 30 * 60 * 1000, // 30分钟
  },
  network: {
    connectionTimeout: 15000, // 15秒
    readTimeout: 60000, // 60秒
    retry: {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
    },
    enableDeduplication: true,
    deduplicationWindow: 2000, // 2秒
  },
};

/**
 * 测试环境性能配置
 */
export const TEST_PERFORMANCE_CONFIG: PerformanceConfig = {
  cache: {
    maxSize: 100,
    defaultTTL: 30 * 1000, // 30秒
    enabled: false, // 测试时禁用缓存
    cleanupInterval: 10 * 1000, // 10秒
  },
  concurrency: {
    maxConcurrency: 2,
    maxQueueSize: 10,
    enabled: true,
  },
  monitoring: {
    enabled: true,
    slowOperationThreshold: 500, // 0.5秒
    detailedTiming: true,
    dataRetentionTime: 5 * 60 * 1000, // 5分钟
  },
  network: {
    connectionTimeout: 5000, // 5秒
    readTimeout: 10000, // 10秒
    retry: {
      maxAttempts: 1,
      baseDelay: 100,
      maxDelay: 1000,
      backoffFactor: 1,
    },
    enableDeduplication: false,
    deduplicationWindow: 0,
  },
};

/**
 * 获取当前环境的性能配置
 */
export function getPerformanceConfig(): PerformanceConfig {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return PRODUCTION_PERFORMANCE_CONFIG;
    case 'test':
      return TEST_PERFORMANCE_CONFIG;
    case 'development':
    default:
      return DEVELOPMENT_PERFORMANCE_CONFIG;
  }
}

/**
 * 性能配置验证器
 */
export class PerformanceConfigValidator {
  static validate(config: PerformanceConfig): string[] {
    const errors: string[] = [];

    // 验证缓存配置
    if (config.cache.maxSize <= 0) {
      errors.push('缓存最大大小必须大于0');
    }
    if (config.cache.defaultTTL <= 0) {
      errors.push('缓存默认TTL必须大于0');
    }
    if (config.cache.cleanupInterval <= 0) {
      errors.push('缓存清理间隔必须大于0');
    }

    // 验证并发配置
    if (config.concurrency.maxConcurrency <= 0) {
      errors.push('最大并发数必须大于0');
    }
    if (config.concurrency.maxQueueSize <= 0) {
      errors.push('队列最大长度必须大于0');
    }

    // 验证监控配置
    if (config.monitoring.slowOperationThreshold <= 0) {
      errors.push('慢操作阈值必须大于0');
    }
    if (config.monitoring.dataRetentionTime <= 0) {
      errors.push('性能数据保留时间必须大于0');
    }

    // 验证网络配置
    if (config.network.connectionTimeout <= 0) {
      errors.push('连接超时必须大于0');
    }
    if (config.network.readTimeout <= 0) {
      errors.push('读取超时必须大于0');
    }
    if (config.network.retry.maxAttempts < 0) {
      errors.push('重试次数不能为负数');
    }
    if (config.network.retry.baseDelay <= 0) {
      errors.push('重试基础延迟必须大于0');
    }

    return errors;
  }

  static validateAndThrow(config: PerformanceConfig): void {
    const errors = this.validate(config);
    if (errors.length > 0) {
      throw new Error(`性能配置验证失败: ${errors.join(', ')}`);
    }
  }
}

/**
 * 性能配置管理器
 */
export class PerformanceConfigManager {
  private static instance: PerformanceConfigManager;
  private config: PerformanceConfig;

  private constructor() {
    this.config = getPerformanceConfig();
    PerformanceConfigValidator.validateAndThrow(this.config);
  }

  static getInstance(): PerformanceConfigManager {
    if (!this.instance) {
      this.instance = new PerformanceConfigManager();
    }
    return this.instance;
  }

  getConfig(): PerformanceConfig {
    return { ...this.config }; // 返回副本
  }

  updateConfig(updates: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...updates };
    PerformanceConfigValidator.validateAndThrow(this.config);
  }

  resetToDefault(): void {
    this.config = getPerformanceConfig();
  }
}

/**
 * 导出默认配置管理器实例
 */
export const performanceConfigManager = PerformanceConfigManager.getInstance();
